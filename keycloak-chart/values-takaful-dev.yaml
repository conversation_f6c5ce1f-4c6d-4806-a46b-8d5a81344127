# Takaful Dev Environment specific values for Keycloak
# Override default values for development environment

replicaCount: 1

image:
  repository: quay.io/keycloak/keycloak
  pullPolicy: Always
  tag: "23.0.0"

service:
  type: ClusterIP
  port: 8080

ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    # For development, we might not need SSL
    # cert-manager.io/cluster-issuer: "letsencrypt-staging"
  hosts:
    - host: keycloak.takaful-dev.local
      paths:
        - path: /
          pathType: Prefix
  # Disable TLS for development
  tls: []

resources:
  limits:
    cpu: 500m
    memory: 1Gi
  requests:
    cpu: 250m
    memory: 512Mi

# Keycloak specific configuration for dev
keycloak:
  admin:
    username: admin
    existingSecret: keycloak-admin-secret
    passwordKey: password
  
  database:
    vendor: postgres
    hostname: ""
    port: 5432
    database: keycloak
    username: keycloak
    existingSecret: keycloak-db-secret
    passwordKey: password
  
  # Development-friendly environment variables
  extraEnv:
    - name: KC_HOSTNAME_STRICT
      value: "false"
    - name: KC_HOSTNAME_STRICT_HTTPS
      value: "false"
    - name: KC_HTTP_ENABLED
      value: "true"
    - name: KC_HEALTH_ENABLED
      value: "true"
    - name: KC_METRICS_ENABLED
      value: "true"
    - name: KC_PROXY
      value: "edge"
    - name: KC_LOG_LEVEL
      value: "INFO"
    - name: KC_HOSTNAME
      value: "keycloak.takaful-dev.local"
    - name: KC_HOSTNAME_ADMIN
      value: "keycloak.takaful-dev.local"
  
  javaOpts: "-Xms256m -Xmx512m -XX:+UseG1GC"

# PostgreSQL configuration for development
postgresql:
  enabled: true
  auth:
    postgresPassword: "postgres123"
    username: "keycloak"
    password: "keycloak123"
    database: "keycloak"
  primary:
    persistence:
      enabled: true
      size: 5Gi
      # Use default storage class for dev
      storageClass: ""
    resources:
      limits:
        cpu: 250m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 256Mi
    # Enable logging for development
    extendedConfiguration: |
      log_statement = 'all'
      log_min_duration_statement = 0

# Disable autoscaling in development
autoscaling:
  enabled: false

# Monitoring configuration for dev
monitoring:
  enabled: true
  serviceMonitor:
    enabled: false  # Disable for dev unless Prometheus is available
    interval: 30s
    path: /metrics

# Development specific node selector (if needed)
nodeSelector: {}

# No special tolerations needed for dev
tolerations: []

# No special affinity rules for dev
affinity: {}
