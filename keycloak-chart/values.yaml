# Default values for keycloak
# This is a YAML-formatted file.

replicaCount: 2

image:
  repository: quay.io/keycloak/keycloak
  pullPolicy: IfNotPresent
  tag: "23.0.0"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations: {}

podSecurityContext:
  fsGroup: 1000

securityContext:
  allowPrivilegeEscalation: false
  runAsNonRoot: true
  runAsUser: 1000
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: false

service:
  type: ClusterIP
  port: 8080
  targetPort: 8080

ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  hosts:
    - host: keycloak.takaful-dev.local
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: keycloak-tls
      hosts:
        - keycloak.takaful-dev.local

resources:
  limits:
    cpu: 1000m
    memory: 2Gi
  requests:
    cpu: 500m
    memory: 1Gi

autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

# Keycloak specific configuration
keycloak:
  admin:
    username: admin
    # Password should be set via secret or environment variable
    existingSecret: keycloak-admin-secret
    passwordKey: password
  
  # Database configuration
  database:
    vendor: postgres
    hostname: ""
    port: 5432
    database: keycloak
    username: keycloak
    existingSecret: keycloak-db-secret
    passwordKey: password
  
  # Additional environment variables
  extraEnv:
    - name: KC_HOSTNAME_STRICT
      value: "false"
    - name: KC_HOSTNAME_STRICT_HTTPS
      value: "false"
    - name: KC_HTTP_ENABLED
      value: "true"
    - name: KC_HEALTH_ENABLED
      value: "true"
    - name: KC_METRICS_ENABLED
      value: "true"
    - name: KC_PROXY
      value: "edge"
  
  # JVM options
  javaOpts: "-Xms512m -Xmx1024m"

# PostgreSQL dependency configuration
postgresql:
  enabled: true
  auth:
    postgresPassword: "postgres123"
    username: "keycloak"
    password: "keycloak123"
    database: "keycloak"
  primary:
    persistence:
      enabled: true
      size: 10Gi
      storageClass: ""
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi

# External database configuration (when postgresql.enabled = false)
externalDatabase:
  url: ""
  username: ""
  password: ""
  existingSecret: ""
  passwordKey: ""

# Monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    interval: 30s
    path: /metrics
