apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "keycloak.fullname" . }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "keycloak.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "keycloak.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "keycloak.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      initContainers:
        - name: wait-for-db
          image: busybox:1.35
          command: ['sh', '-c']
          args:
            - |
              until nc -z {{ if .Values.postgresql.enabled }}{{ .Release.Name }}-postgresql{{ else }}{{ .Values.externalDatabase.hostname }}{{ end }} {{ if .Values.postgresql.enabled }}5432{{ else }}{{ .Values.externalDatabase.port }}{{ end }}; do
                echo "Waiting for database..."
                sleep 2
              done
              echo "Database is ready!"
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            - /opt/keycloak/bin/kc.sh
          args:
            - start-dev
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          env:
            - name: KEYCLOAK_ADMIN
              value: {{ .Values.keycloak.admin.username }}
            - name: KEYCLOAK_ADMIN_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.keycloak.admin.existingSecret }}
                  key: {{ .Values.keycloak.admin.passwordKey }}
            - name: KC_DB
              value: {{ .Values.keycloak.database.vendor }}
            - name: KC_DB_URL
              value: {{ include "keycloak.dbUrl" . }}
            - name: KC_DB_USERNAME
              value: {{ .Values.keycloak.database.username }}
            - name: KC_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.keycloak.database.existingSecret }}
                  key: {{ .Values.keycloak.database.passwordKey }}
            {{- if .Values.keycloak.javaOpts }}
            - name: JAVA_OPTS
              value: {{ .Values.keycloak.javaOpts }}
            {{- end }}
            {{- with .Values.keycloak.extraEnv }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
          livenessProbe:
            httpGet:
              path: /health/live
              port: http
            initialDelaySeconds: 120
            periodSeconds: 30
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health/ready
              port: http
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
