apiVersion: v1
kind: Secret
metadata:
  name: keycloak-admin-secret
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
type: Opaque
data:
  password: {{ "admin123" | b64enc | quote }}
---
apiVersion: v1
kind: Secret
metadata:
  name: keycloak-db-secret
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
type: Opaque
data:
  {{- if .Values.postgresql.enabled }}
  password: {{ .Values.postgresql.auth.password | b64enc | quote }}
  {{- else }}
  password: {{ .Values.externalDatabase.password | b64enc | quote }}
  {{- end }}
