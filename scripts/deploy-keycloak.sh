#!/bin/bash

# Deploy Keycloak to takaful-dev environment using Helm
# Usage: ./deploy-keycloak.sh [environment]

set -e

# Configuration
ENVIRONMENT=${1:-takaful-dev}
NAMESPACE="keycloak-${ENVIRONMENT}"
RELEASE_NAME="keycloak-${ENVIRONMENT}"
CHART_PATH="./keycloak-chart"
VALUES_FILE="./keycloak-chart/values-${ENVIRONMENT}.yaml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Deploying Keycloak to ${ENVIRONMENT} environment${NC}"

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}❌ kubectl is not installed or not in PATH${NC}"
    exit 1
fi

# Check if helm is available
if ! command -v helm &> /dev/null; then
    echo -e "${RED}❌ Helm is not installed or not in PATH${NC}"
    exit 1
fi

# Check if values file exists
if [ ! -f "$VALUES_FILE" ]; then
    echo -e "${RED}❌ Values file not found: $VALUES_FILE${NC}"
    exit 1
fi

# Create namespace if it doesn't exist
echo -e "${YELLOW}📦 Creating namespace: $NAMESPACE${NC}"
kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -

# Add Bitnami repository for PostgreSQL dependency
echo -e "${YELLOW}📚 Adding Bitnami Helm repository${NC}"
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo update

# Update dependencies
echo -e "${YELLOW}🔄 Updating Helm dependencies${NC}"
cd "$CHART_PATH"
helm dependency update
cd ..

# Deploy or upgrade Keycloak
echo -e "${YELLOW}🚀 Deploying Keycloak with Helm${NC}"
helm upgrade --install "$RELEASE_NAME" "$CHART_PATH" \
    --namespace "$NAMESPACE" \
    --values "$VALUES_FILE" \
    --wait \
    --timeout=10m

# Check deployment status
echo -e "${YELLOW}🔍 Checking deployment status${NC}"
kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name=keycloak

# Get service information
echo -e "${YELLOW}🌐 Service information${NC}"
kubectl get svc -n "$NAMESPACE" -l app.kubernetes.io/name=keycloak

# Get ingress information
echo -e "${YELLOW}🌍 Ingress information${NC}"
kubectl get ingress -n "$NAMESPACE" -l app.kubernetes.io/name=keycloak

echo -e "${GREEN}✅ Keycloak deployment completed successfully!${NC}"
echo -e "${GREEN}🔗 Access Keycloak at: http://keycloak.takaful-dev.local${NC}"
echo -e "${GREEN}👤 Admin credentials:${NC}"
echo -e "${GREEN}   Username: admin${NC}"
echo -e "${GREEN}   Password: admin123 (stored in keycloak-admin-secret)${NC}"

# Show next steps
echo -e "${YELLOW}📋 Next steps:${NC}"
echo "1. Add 'keycloak.takaful-dev.local' to your /etc/hosts file pointing to your ingress controller IP"
echo "2. Wait for all pods to be ready: kubectl get pods -n $NAMESPACE -w"
echo "3. Check logs if needed: kubectl logs -n $NAMESPACE -l app.kubernetes.io/name=keycloak"
echo "4. Access the admin console at: http://keycloak.takaful-dev.local/admin"
