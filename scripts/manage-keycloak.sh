#!/bin/bash

# Keycloak management script for takaful-dev environment
# Usage: ./manage-keycloak.sh [command] [environment]

set -e

# Configuration
ENVIRONMENT=${2:-takaful-dev}
NAMESPACE="keycloak-${ENVIRONMENT}"
RELEASE_NAME="keycloak-${ENVIRONMENT}"
CHART_PATH="./keycloak-chart"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to show usage
show_usage() {
    echo -e "${BLUE}Keycloak Management Script${NC}"
    echo ""
    echo "Usage: $0 [command] [environment]"
    echo ""
    echo "Commands:"
    echo "  status      - Show deployment status"
    echo "  logs        - Show Keycloak logs"
    echo "  restart     - Restart Keycloak pods"
    echo "  scale       - Scale Keycloak deployment"
    echo "  delete      - Delete Keycloak deployment"
    echo "  upgrade     - Upgrade Keycloak deployment"
    echo "  port-forward - Port forward to Keycloak service"
    echo "  backup-db   - Backup PostgreSQL database"
    echo "  restore-db  - Restore PostgreSQL database"
    echo "  help        - Show this help message"
    echo ""
    echo "Environment: ${ENVIRONMENT} (default: takaful-dev)"
}

# Function to check prerequisites
check_prerequisites() {
    if ! command -v kubectl &> /dev/null; then
        echo -e "${RED}❌ kubectl is not installed or not in PATH${NC}"
        exit 1
    fi

    if ! command -v helm &> /dev/null; then
        echo -e "${RED}❌ Helm is not installed or not in PATH${NC}"
        exit 1
    fi
}

# Function to show status
show_status() {
    echo -e "${GREEN}📊 Keycloak Status for ${ENVIRONMENT}${NC}"
    echo ""
    
    echo -e "${YELLOW}Helm Release:${NC}"
    helm status "$RELEASE_NAME" -n "$NAMESPACE" || echo "Release not found"
    echo ""
    
    echo -e "${YELLOW}Pods:${NC}"
    kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name=keycloak
    echo ""
    
    echo -e "${YELLOW}Services:${NC}"
    kubectl get svc -n "$NAMESPACE" -l app.kubernetes.io/name=keycloak
    echo ""
    
    echo -e "${YELLOW}Ingress:${NC}"
    kubectl get ingress -n "$NAMESPACE" -l app.kubernetes.io/name=keycloak
}

# Function to show logs
show_logs() {
    echo -e "${GREEN}📋 Keycloak Logs for ${ENVIRONMENT}${NC}"
    kubectl logs -n "$NAMESPACE" -l app.kubernetes.io/name=keycloak --tail=100 -f
}

# Function to restart pods
restart_pods() {
    echo -e "${GREEN}🔄 Restarting Keycloak pods for ${ENVIRONMENT}${NC}"
    kubectl rollout restart deployment -n "$NAMESPACE" -l app.kubernetes.io/name=keycloak
    kubectl rollout status deployment -n "$NAMESPACE" -l app.kubernetes.io/name=keycloak
}

# Function to scale deployment
scale_deployment() {
    read -p "Enter number of replicas: " replicas
    echo -e "${GREEN}📈 Scaling Keycloak to ${replicas} replicas${NC}"
    kubectl scale deployment -n "$NAMESPACE" -l app.kubernetes.io/name=keycloak --replicas="$replicas"
}

# Function to delete deployment
delete_deployment() {
    echo -e "${RED}⚠️  This will delete the entire Keycloak deployment!${NC}"
    read -p "Are you sure? (yes/no): " confirm
    if [ "$confirm" = "yes" ]; then
        echo -e "${RED}🗑️  Deleting Keycloak deployment${NC}"
        helm uninstall "$RELEASE_NAME" -n "$NAMESPACE"
        kubectl delete namespace "$NAMESPACE" --ignore-not-found=true
    else
        echo "Deletion cancelled"
    fi
}

# Function to upgrade deployment
upgrade_deployment() {
    VALUES_FILE="./keycloak-chart/values-${ENVIRONMENT}.yaml"
    echo -e "${GREEN}⬆️  Upgrading Keycloak deployment${NC}"
    helm upgrade "$RELEASE_NAME" "$CHART_PATH" \
        --namespace "$NAMESPACE" \
        --values "$VALUES_FILE" \
        --wait \
        --timeout=10m
}

# Function to port forward
port_forward() {
    echo -e "${GREEN}🔗 Port forwarding Keycloak service${NC}"
    echo "Access Keycloak at: http://localhost:8080"
    kubectl port-forward -n "$NAMESPACE" svc/keycloak-takaful-dev 8080:8080
}

# Function to backup database
backup_db() {
    echo -e "${GREEN}💾 Backing up PostgreSQL database${NC}"
    BACKUP_FILE="keycloak-backup-$(date +%Y%m%d-%H%M%S).sql"
    kubectl exec -n "$NAMESPACE" -it deployment/keycloak-takaful-dev-postgresql -- \
        pg_dump -U keycloak keycloak > "$BACKUP_FILE"
    echo "Database backed up to: $BACKUP_FILE"
}

# Function to restore database
restore_db() {
    read -p "Enter backup file path: " backup_file
    if [ -f "$backup_file" ]; then
        echo -e "${GREEN}🔄 Restoring PostgreSQL database${NC}"
        kubectl exec -n "$NAMESPACE" -i deployment/keycloak-takaful-dev-postgresql -- \
            psql -U keycloak keycloak < "$backup_file"
        echo "Database restored successfully"
    else
        echo -e "${RED}❌ Backup file not found: $backup_file${NC}"
    fi
}

# Main script logic
check_prerequisites

case "${1:-help}" in
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    restart)
        restart_pods
        ;;
    scale)
        scale_deployment
        ;;
    delete)
        delete_deployment
        ;;
    upgrade)
        upgrade_deployment
        ;;
    port-forward)
        port_forward
        ;;
    backup-db)
        backup_db
        ;;
    restore-db)
        restore_db
        ;;
    help|*)
        show_usage
        ;;
esac
