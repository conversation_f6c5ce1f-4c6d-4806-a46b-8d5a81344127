# Keycloak Deployment for Takaful Dev Environment

This repository contains Helm charts and scripts for deploying Keycloak in the takaful-dev environment.

## Overview

Keycloak is an open-source identity and access management solution that provides:
- Single Sign-On (SSO)
- Identity brokering and social login
- User federation
- Client adapters
- Admin console
- Account management console

## Prerequisites

Before deploying Keycloak, ensure you have:

1. **Kubernetes cluster** - A running Kubernetes cluster with kubectl configured
2. **Helm 3.x** - Package manager for Kubernetes
3. **Ingress Controller** - NGINX ingress controller (recommended)
4. **Storage Class** - For PostgreSQL persistent volumes
5. **DNS/Hosts** - Ability to resolve `keycloak.takaful-dev.local`

## Quick Start

### 1. Clone and Navigate
```bash
cd /path/to/this/repository
```

### 2. Make Scripts Executable
```bash
chmod +x scripts/*.sh
```

### 3. Deploy Keycloak
```bash
./scripts/deploy-keycloak.sh takaful-dev
```

### 4. Access Keycloak
Add to your `/etc/hosts` file:
```
<INGRESS_IP> keycloak.takaful-dev.local
```

Then access:
- **Admin Console**: http://keycloak.takaful-dev.local/admin
- **Account Console**: http://keycloak.takaful-dev.local/realms/master/account
- **Username**: admin
- **Password**: admin123

## Architecture

### Components
- **Keycloak**: Main identity server (2 replicas in production, 1 in dev)
- **PostgreSQL**: Database backend (managed by Bitnami chart)
- **NGINX Ingress**: External access and SSL termination
- **Secrets**: Secure storage for passwords and keys

### Network Flow
```
Internet → Ingress Controller → Keycloak Service → Keycloak Pods
                                      ↓
                              PostgreSQL Service → PostgreSQL Pod
```

## Configuration

### Environment-Specific Values

#### Development (takaful-dev)
- File: `keycloak-chart/values-takaful-dev.yaml`
- Features:
  - Single replica
  - Reduced resource requirements
  - HTTP-only (no SSL)
  - Debug logging enabled
  - Embedded PostgreSQL

#### Production (customize as needed)
- File: `keycloak-chart/values-production.yaml` (create if needed)
- Features:
  - Multiple replicas
  - High availability
  - SSL/TLS enabled
  - External database
  - Resource limits

### Key Configuration Options

```yaml
# Replica count
replicaCount: 1

# Image configuration
image:
  repository: quay.io/keycloak/keycloak
  tag: "23.0.0"

# Ingress configuration
ingress:
  enabled: true
  hosts:
    - host: keycloak.takaful-dev.local

# Database configuration
postgresql:
  enabled: true
  auth:
    database: keycloak
    username: keycloak
```

## Management Scripts

### Deploy Script
```bash
./scripts/deploy-keycloak.sh [environment]
```
- Deploys Keycloak using Helm
- Creates namespace
- Updates dependencies
- Configures ingress

### Management Script
```bash
./scripts/manage-keycloak.sh [command] [environment]
```

Available commands:
- `status` - Show deployment status
- `logs` - Show Keycloak logs
- `restart` - Restart Keycloak pods
- `scale` - Scale deployment
- `upgrade` - Upgrade deployment
- `delete` - Delete deployment
- `port-forward` - Port forward for local access
- `backup-db` - Backup PostgreSQL database
- `restore-db` - Restore PostgreSQL database

## Troubleshooting

### Common Issues

1. **Pods not starting**
   ```bash
   kubectl describe pods -n keycloak-takaful-dev
   kubectl logs -n keycloak-takaful-dev -l app.kubernetes.io/name=keycloak
   ```

2. **Database connection issues**
   ```bash
   kubectl logs -n keycloak-takaful-dev -l app.kubernetes.io/name=postgresql
   ```

3. **Ingress not working**
   ```bash
   kubectl get ingress -n keycloak-takaful-dev
   kubectl describe ingress -n keycloak-takaful-dev
   ```

### Health Checks

Keycloak provides health endpoints:
- **Liveness**: `/health/live`
- **Readiness**: `/health/ready`
- **Metrics**: `/metrics` (if enabled)

### Accessing Logs
```bash
# Real-time logs
./scripts/manage-keycloak.sh logs

# Or directly with kubectl
kubectl logs -n keycloak-takaful-dev -l app.kubernetes.io/name=keycloak -f
```

## Security Considerations

### Default Credentials
⚠️ **Important**: Change default passwords in production!

- Admin password is stored in `keycloak-admin-secret`
- Database password is stored in `keycloak-db-secret`

### SSL/TLS
For production environments:
1. Enable SSL in ingress configuration
2. Use cert-manager for automatic certificate management
3. Configure proper SSL policies

### Network Policies
Consider implementing Kubernetes Network Policies to restrict traffic between components.

## Backup and Recovery

### Database Backup
```bash
./scripts/manage-keycloak.sh backup-db
```

### Database Restore
```bash
./scripts/manage-keycloak.sh restore-db
```

### Configuration Backup
Export Keycloak realm configurations through the admin console or use the export feature.

## Monitoring

### Metrics
Keycloak exposes Prometheus metrics at `/metrics` endpoint when enabled.

### Alerts
Set up alerts for:
- Pod restarts
- High memory/CPU usage
- Database connection issues
- Failed authentication attempts

## Scaling

### Horizontal Scaling
```bash
./scripts/manage-keycloak.sh scale
```

### Vertical Scaling
Modify resource requests/limits in values files and upgrade.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review Keycloak documentation: https://www.keycloak.org/documentation
3. Check Kubernetes cluster logs
4. Contact the development team
